package signature

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type SignatureDomainItf interface {
	GetList(ctx context.Context, param GetListParam) ([]Signature, error)
}

type SignatureResourceItf interface {
	getList(ctx context.Context, param GetListParam) ([]Signature, error)
}

func (d SignatureDomain) GetList(ctx context.Context, param GetListParam) ([]Signature, error) {
	data, err := d.resource.getList(ctx, param)
	if err != nil {
		return []Signature{}, log.LogError(err, nil)
	}

	return data, nil
}
