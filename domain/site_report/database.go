package sitereport

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches site reports filtered by date range.
func (rsc SiteReportResource) getList(ctx context.Context, param GetListParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)

	// Filter by work_date range and exclude soft deleted records
	err := db.Where("work_date >= ? AND work_date <= ?", param.StartDate, param.EndDate).
		Where("deleted_at IS NULL").
		Order("work_date ASC, id ASC").
		Find(&reports).Error

	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}

// getInvoiceList fetches site reports with customer relationships filtered by date range for invoice list.
func (rsc SiteReportResource) getInvoiceList(ctx context.Context, param GetInvoiceListParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)

	// Filter by work_date range and exclude soft deleted records
	err := db.Where("work_date >= ? AND work_date <= ?", param.StartDate, param.EndDate).
		Where("deleted_at IS NULL").
		Preload("DepartmentPic.Department.Customer").
		Order("work_date ASC, id ASC").
		Find(&reports).Error

	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}

// getStatutoryList fetches site reports with statutory data and customer relationships filtered by date range for statutory list.
func (rsc SiteReportResource) getStatutoryList(ctx context.Context, param GetStatutoryListParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)

	// Filter by work_date range, has_statutory=true, and exclude soft deleted records
	err := db.Where("work_date >= ? AND work_date <= ?", param.StartDate, param.EndDate).
		Where("has_statutory = ?", true).
		Where("deleted_at IS NULL").
		Preload("DepartmentPic.Department.Customer").
		Preload("Statutory").
		Order("work_date ASC, id ASC").
		Find(&reports).Error

	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}

// bulkUpdate updates multiple site reports with the specified fields.
func (rsc SiteReportResource) bulkUpdate(ctx context.Context, param BulkUpdateParam) error {
	db := dbmanager.Manager().WithContext(ctx)

	// Build update map with only the fields that are provided
	updateFields := make(map[string]interface{})

	if param.WorkDate != nil {
		updateFields["work_date"] = *param.WorkDate
	}

	if param.IsLocked != nil {
		updateFields["is_locked"] = *param.IsLocked
	}

	if param.IsInvoiceIssued != nil {
		updateFields["is_invoice_issued"] = *param.IsInvoiceIssued
	}

	// Perform bulk update for the specified site report IDs
	err := db.Model(&SiteReport{}).
		Where("id IN ?", param.SiteReportIDs).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getDetailList fetches site reports with detailed information filtered by work_date or id.
func (rsc SiteReportResource) getDetailList(ctx context.Context, param GetDetailListParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)

	// Build query with filters
	query := db.Where("deleted_at IS NULL")

	if param.WorkDate != nil {
		query = query.Where("work_date = ?", *param.WorkDate)
	}

	if param.ID != nil {
		query = query.Where("id = ?", *param.ID)
	}

	if len(param.IDs) > 0 {
		query = query.Where("id IN ?", param.IDs)
	}

	// Use GORM Preload to eagerly load related data in a single query
	// This will create JOINs to fetch all related data at once
	if param.IsPreload {
		query = query.
			Preload("DepartmentPic.Department.Customer.Statutory").
			Preload("BasicPrice").
			Preload("DistrictBlock").
			Preload("TransitPlaceBlock").
			Preload("Statutory").
			Preload("Options.Option").
			Preload("Workers.User").
			Preload("Workers.DistantFee").
			Preload("Workers.Qualifications.Qualification").
			Preload("DailyReportAdditions.DailyReportAddition")
	}
	// Execute query
	err := query.Order("id ASC").Find(&reports).Error
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}

// getByIDs fetches site reports by a list of IDs with proper preloading of relationships.
func (rsc SiteReportResource) getByIDs(ctx context.Context, param GetByIDsParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)

	// Build query with filters
	query := db.Where("id IN ?", param.IDs).
		Where("deleted_at IS NULL")

	// Use GORM Preload to eagerly load related data in a single query
	if param.IsPreload {
		query = query.
			Preload("DepartmentPic.Department.Customer.Statutory").
			Preload("DepartmentPic.Department.TransferDestination").
			Preload("BasicPrice").
			Preload("DistrictBlock").
			Preload("TransitPlaceBlock").
			Preload("Statutory").
			Preload("Options.Option").
			Preload("Workers.User").
			Preload("Workers.DistantFee").
			Preload("Workers.Qualifications.Qualification").
			Preload("DailyReportAdditions.DailyReportAddition")
	}

	// Execute query
	err := query.Order("id ASC").Find(&reports).Error
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}

// createSiteReport creates a new site report record.
func (rsc SiteReportResource) createSiteReport(ctx context.Context, param CreateSiteReportParam) (int64, error) {
	db := dbmanager.Manager().WithContext(ctx)

	siteReport := SiteReport{
		WorkDate:            param.WorkDate,
		SiteName:            param.SiteName,
		DepartmentPicID:     param.DepartmentPicID,
		HasStatutory:        param.HasStatutory,
		BillDate:            param.BillDate,
		BasicPriceID:        param.BasicPriceID,
		Worker:              param.Worker,
		DistrictBlockID:     param.DistrictBlockID,
		DistrictBlockUnit:   param.DistrictBlockUnit,
		TransitPlaceBlockID: param.TransitPlaceBlockID,
		TransitPlaceUnit:    param.TransitPlaceUnit,
		BStartTime:          param.BStartTime,
		BEndTime:            param.BEndTime,
		SStartTime:          param.SStartTime,
		SEndTime:            param.SEndTime,
		BreakTime:           param.BreakTime,
		LateEarlyWorker:     param.LateEarlyWorker,
		ExtraTimeCharge:     param.ExtraTimeCharge,
		Note:                param.Note,
		NoteForInvoice:      param.NoteForInvoice,
		TotalAmount:         param.TotalAmount,
		Snapshot:            param.Snapshot,
		IsLocked:            param.IsLocked,
		IsInvoiceIssued:     param.IsInvoiceIssued,
		CreatedBy:           param.CreatedBy,
		CreatedAt:           param.CreatedAt,
		UpdatedAt:           param.UpdatedAt,
	}

	err := db.Create(&siteReport).Error
	if err != nil {
		return 0, log.LogError(err, nil)
	}

	return siteReport.ID, nil
}

// updateSiteReport updates an existing site report record.
func (rsc SiteReportResource) updateSiteReport(ctx context.Context, param UpdateSiteReportParam) error {
	db := dbmanager.Manager().WithContext(ctx)

	updateFields := map[string]interface{}{
		"work_date":              param.WorkDate,
		"site_name":              param.SiteName,
		"department_pic_id":      param.DepartmentPicID,
		"has_statutory":          param.HasStatutory,
		"bill_date":              param.BillDate,
		"basic_price_id":         param.BasicPriceID,
		"worker":                 param.Worker,
		"district_block_id":      param.DistrictBlockID,
		"district_block_unit":    param.DistrictBlockUnit,
		"transit_place_block_id": param.TransitPlaceBlockID,
		"transit_place_unit":     param.TransitPlaceUnit,
		"b_start_time":           param.BStartTime,
		"b_end_time":             param.BEndTime,
		"s_start_time":           param.SStartTime,
		"s_end_time":             param.SEndTime,
		"break_time":             param.BreakTime,
		"late_early_worker":      param.LateEarlyWorker,
		"extra_time_charge":      param.ExtraTimeCharge,
		"note":                   param.Note,
		"note_for_invoice":       param.NoteForInvoice,
		"total_amount":           param.TotalAmount,
		"snapshot":               param.Snapshot,
		"updated_at":             param.UpdatedAt,
	}

	err := db.Model(&SiteReport{}).
		Where("id = ?", param.ID).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// deleteSiteReport soft deletes a site report record.
func (rsc SiteReportResource) deleteSiteReport(ctx context.Context, param DeleteSiteReportParam) error {
	db := dbmanager.Manager().WithContext(ctx)

	err := db.Model(&SiteReport{}).
		Where("id = ?", param.ID).
		Update("deleted_at", param.DeletedAt).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}
