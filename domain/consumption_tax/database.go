package consumptiontax

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getCurrentRate fetches the current consumption tax rate based on the date.
// Returns the most recent rate where start_date <= provided date
func (rsc ConsumptionTaxResource) getCurrentRate(ctx context.Context, param GetCurrentRateParam) (ConsumptionTax, error) {
	var consumptionTax ConsumptionTax

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("start_date <= ?", param.Date).
		Where("deleted_at IS NULL").
		Order("start_date DESC").
		First(&consumptionTax).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return ConsumptionTax{}, nil
		}
		return ConsumptionTax{}, log.LogError(err, nil)
	}

	return consumptionTax, nil
}
