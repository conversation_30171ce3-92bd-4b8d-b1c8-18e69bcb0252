package consumptiontax

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	ConsumptionTaxDomainItf interface {
		GetCurrentRate(ctx context.Context, param GetCurrentRateParam) (ConsumptionTax, error)
	}

	ConsumptionTaxResourceItf interface {
		getCurrentRate(ctx context.Context, param GetCurrentRateParam) (ConsumptionTax, error)
	}
)

// GetCurrentRate retrieves the current consumption tax rate based on the date.
func (d *ConsumptionTaxDomain) GetCurrentRate(ctx context.Context, param GetCurrentRateParam) (ConsumptionTax, error) {
	consumptionTax, err := d.resource.getCurrentRate(ctx, param)
	if err != nil {
		return ConsumptionTax{}, log.LogError(err, nil)
	}
	return consumptionTax, nil
}
