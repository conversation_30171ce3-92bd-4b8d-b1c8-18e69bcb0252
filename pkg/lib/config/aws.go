package config

import (
	"context"
	"fmt"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

func AwsInit() {
	cfg := GetConfig()

	// will automatically load credentials from env variables
	sess, err := config.LoadDefaultConfig(context.Background(),
		config.WithRegion(cfg.AwsRegion),
	)
	if err != nil {
		fmt.Println("Failed to create AWS session:", err)
		return
	}

	s3Client := s3.NewFromConfig(sess)
	aws.Set(aws.AwsS3Manager{
		Client:  s3Client,
		Bucket:  cfg.AwsS3Bucket,
		BaseUrl: cfg.AwsBaseUrl,
	})

	fmt.Println("Connected to AWS S3")
}
