package customer

import (
	"time"

	"strconv"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetCustomerTable configures GoAdmin CRUD for the customer table.
func GetCustomerTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("顧客コード", "code", db.Varchar)
	info.AddField("顧客名", "name", db.Varchar)
	info.AddField("フリガナ", "furigana", db.Varchar)
	info.AddField("郵便番号", "post_code", db.Varchar)
	info.AddField("住所(都道府県)", "address_prefecture", db.Varchar)
	info.AddField("住所(市町村)", "address_city", db.Varchar)
	info.AddField("住所(ビル名)", "address_building", db.Varchar)
	info.AddField("電話番号", "telephone", db.Varchar)
	info.AddField("FAZ番号", "fax", db.Varchar)
	info.AddField("請求日", "billing_date", db.Date)
	info.AddField("法定福利支援金率", "statutory_id", db.Bigint).FieldDisplay(func(field types.FieldModel) interface{} {
		gormDB := dbmanager.Manager()
		var title string
		if gormDB != nil {
			_ = gormDB.Table("statutory").Select("title").Where("id = ?", field.Value).Scan(&title).Error
		}
		if title == "" {
			return field.Value
		}
		return title
	})
	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})

	info.SetTable("customer").SetTitle("顧客一覧")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("customer").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft-deleted
	info.WhereRaw("\"customer\".deleted_at IS NULL")

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("顧客コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("顧客名", "name", db.Varchar, form.Text).FieldMust()
	formList.AddField("フリガナ", "furigana", db.Varchar, form.Text).FieldMust()
	formList.AddField("郵便番号", "post_code", db.Varchar, form.Text).FieldMust()
	formList.AddField("住所(都道府県)", "address_prefecture", db.Varchar, form.Text).FieldMust()
	formList.AddField("住所(市町村)", "address_city", db.Varchar, form.Text).FieldMust()
	formList.AddField("住所(ビル名)", "address_building", db.Varchar, form.Text).FieldMust()
	formList.AddField("電話番号", "telephone", db.Varchar, form.Text).FieldMust()
	formList.AddField("FAZ番号", "fax", db.Varchar, form.Text).FieldMust()
	formList.AddField("請求日", "billing_date", db.Date, form.Date).FieldMust()
	formList.AddField("法定福利支援金率", "statutory_id", db.Bigint, form.SelectSingle).
		FieldOptions(statutoryOptions())

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("customer").SetTitle("顧客")

	return tbl
}

// statutoryOptions builds dropdown options excluding soft-deleted statutory records.
func statutoryOptions() types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}
	var items []struct {
		ID    int
		Title string
	}
	db.Table("statutory").Select("id, title").Where("deleted_at IS NULL").Find(&items)
	opts := make(types.FieldOptions, 0, len(items))
	for _, s := range items {
		opts = append(opts, types.FieldOption{
			Text:  s.Title,
			Value: strconv.Itoa(s.ID),
		})
	}
	return opts
}
