package consumptiontax

import (
	"time"
)

type ConsumptionTax struct {
	ID        int64      `gorm:"column:id;primary_key"`
	Code      string     `gorm:"column:code;unique"`
	StartDate time.Time  `gorm:"column:start_date"`
	Rate      float64    `gorm:"column:rate"`
	CreatedAt time.Time  `gorm:"column:created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"`
}

// GetCurrentRateParam represents the parameters for getting current consumption tax rate
type GetCurrentRateParam struct {
	Date time.Time
}
