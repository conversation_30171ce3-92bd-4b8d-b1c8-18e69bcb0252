package transferdestination

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	TransferDestinationDomainItf interface {
		GetByIDs(ctx context.Context, param GetByIDsParam) (map[int64]TransferDestination, error)
	}

	TransferDestinationResourceItf interface {
		getByIDs(ctx context.Context, param GetByIDsParam) (map[int64]TransferDestination, error)
	}
)

// GetByIDs retrieves transfer destinations by IDs and returns a map for efficient lookup.
func (d *TransferDestinationDomain) GetByIDs(ctx context.Context, param GetByIDsParam) (map[int64]TransferDestination, error) {
	transferDestinationMap, err := d.resource.getByIDs(ctx, param)
	if err != nil {
		return nil, log.LogError(err, nil)
	}
	return transferDestinationMap, nil
}
