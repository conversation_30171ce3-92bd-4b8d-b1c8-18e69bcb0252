package signature

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches signatures with optional type filtering ordered by name.
func (rsc SignatureResource) getList(ctx context.Context, param GetListParam) ([]Signature, error) {
	var signatures []Signature

	db := dbmanager.Manager().WithContext(ctx)

	// Apply type-based filtering
	switch param.Type {
	case "pic":
		db = db.Where("is_pic = ?", true)
	case "boss":
		db = db.Where("is_boss = ?", true)
	}

	// Exclude soft deleted records and order by name ascending
	err := db.Where("deleted_at IS NULL").
		Order("name ASC").
		Find(&signatures).Error

	if err != nil {
		return []Signature{}, log.LogError(err, nil)
	}

	return signatures, nil
}
