package report

import (
	"strconv"
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetDistrictTable configures GoAdmin CRUD for the district table.
func GetDistrictTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("コード", "code", db.Varchar)
	info.AddField("ブロック", "block_id", db.Bigint).FieldDisplay(func(field types.FieldModel) interface{} {
		gormDB := dbmanager.Manager()
		var blockName string
		if gormDB != nil {
			_ = gormDB.Table("block").Select("name").Where("id = ?", field.Value).Scan(&blockName).Error
		}
		if blockName == "" {
			return field.Value
		}
		return blockName
	})
	info.AddField("名前", "name", db.Varchar)
	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})

	info.SetTable("district").SetTitle("地区")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("district").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft deleted
	info.WhereRaw("\"district\".deleted_at IS NULL")

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("ブロック", "block_id", db.Bigint, form.SelectSingle).
		FieldOptions(blockOptions())
	formList.AddField("名前", "name", db.Varchar, form.Text).FieldMust()

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("district").SetTitle("地区")

	return tbl
}

// blockOptions builds dropdown options excluding soft-deleted block records.
func blockOptions() types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}
	var items []struct {
		ID   int
		Name string
	}
	db.Table("block").Select("id, name").Where("deleted_at IS NULL").Find(&items)
	opts := make(types.FieldOptions, 0, len(items))
	for _, b := range items {
		opts = append(opts, types.FieldOption{
			Text:  b.Name,
			Value: strconv.Itoa(b.ID),
		})
	}
	return opts
}
