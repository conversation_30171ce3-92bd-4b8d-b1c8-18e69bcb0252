package sitereport

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	SiteReportDomainItf interface {
		GetList(ctx context.Context, param GetListParam) ([]SiteReport, error)
		BulkUpdate(ctx context.Context, param BulkUpdateParam) error
		GetDetailList(ctx context.Context, param GetDetailListParam) ([]SiteReport, error)
		GetInvoiceList(ctx context.Context, param GetInvoiceListParam) ([]SiteReport, error)
		GetStatutoryList(ctx context.Context, param GetStatutoryListParam) ([]SiteReport, error)
		GetByIDs(ctx context.Context, param GetByIDsParam) ([]SiteReport, error)
		CreateSiteReport(ctx context.Context, param CreateSiteReportParam) (int64, error)
		UpdateSiteReport(ctx context.Context, param UpdateSiteReportParam) error
		DeleteSiteReport(ctx context.Context, param DeleteSiteReportParam) error
	}

	SiteReportResourceItf interface {
		getList(ctx context.Context, param GetListParam) ([]SiteReport, error)
		bulkUpdate(ctx context.Context, param BulkUpdateParam) error
		getDetailList(ctx context.Context, param GetDetailListParam) ([]SiteReport, error)
		getInvoiceList(ctx context.Context, param GetInvoiceListParam) ([]SiteReport, error)
		getStatutoryList(ctx context.Context, param GetStatutoryListParam) ([]SiteReport, error)
		getByIDs(ctx context.Context, param GetByIDsParam) ([]SiteReport, error)
		createSiteReport(ctx context.Context, param CreateSiteReportParam) (int64, error)
		updateSiteReport(ctx context.Context, param UpdateSiteReportParam) error
		deleteSiteReport(ctx context.Context, param DeleteSiteReportParam) error
	}
)

// GetList retrieves site reports filtered by date range.
func (d *SiteReportDomain) GetList(ctx context.Context, param GetListParam) ([]SiteReport, error) {
	reports, err := d.resource.getList(ctx, param)
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}
	return reports, nil
}

// BulkUpdate updates multiple site reports with the specified fields.
func (d *SiteReportDomain) BulkUpdate(ctx context.Context, param BulkUpdateParam) error {
	err := d.resource.bulkUpdate(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// GetDetailList retrieves site reports with detailed information filtered by work_date or id.
func (d *SiteReportDomain) GetDetailList(ctx context.Context, param GetDetailListParam) ([]SiteReport, error) {
	reports, err := d.resource.getDetailList(ctx, param)
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}
	return reports, nil
}

// GetInvoiceList retrieves site reports with customer relationships for invoice list.
func (d *SiteReportDomain) GetInvoiceList(ctx context.Context, param GetInvoiceListParam) ([]SiteReport, error) {
	reports, err := d.resource.getInvoiceList(ctx, param)
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}
	return reports, nil
}

// GetStatutoryList retrieves site reports with statutory data and customer relationships for statutory list.
func (d *SiteReportDomain) GetStatutoryList(ctx context.Context, param GetStatutoryListParam) ([]SiteReport, error) {
	reports, err := d.resource.getStatutoryList(ctx, param)
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}
	return reports, nil
}

// GetByIDs retrieves site reports by a list of IDs with proper preloading of relationships.
func (d *SiteReportDomain) GetByIDs(ctx context.Context, param GetByIDsParam) ([]SiteReport, error) {
	reports, err := d.resource.getByIDs(ctx, param)
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}
	return reports, nil
}

// CreateSiteReport creates a new site report record.
func (d *SiteReportDomain) CreateSiteReport(ctx context.Context, param CreateSiteReportParam) (int64, error) {
	id, err := d.resource.createSiteReport(ctx, param)
	if err != nil {
		return 0, log.LogError(err, nil)
	}
	return id, nil
}

// UpdateSiteReport updates an existing site report record.
func (d *SiteReportDomain) UpdateSiteReport(ctx context.Context, param UpdateSiteReportParam) error {
	err := d.resource.updateSiteReport(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// DeleteSiteReport soft deletes a site report record.
func (d *SiteReportDomain) DeleteSiteReport(ctx context.Context, param DeleteSiteReportParam) error {
	err := d.resource.deleteSiteReport(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}
