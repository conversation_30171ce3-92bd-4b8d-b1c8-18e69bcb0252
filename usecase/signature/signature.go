package signature

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	signatureDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/signature"
)

// GetList retrieves signature list with optional type filtering
func (uc *SignatureUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	signatures, err := uc.signature.GetList(ctx, signatureDmn.GetListParam{
		Type: req.Type,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}

	// Convert domain response to usecase response
	var resp []GetListResp
	for _, signature := range signatures {
		resp = append(resp, GetListResp{
			ID:       signature.ID,
			Name:     signature.Name,
			ImageURL: signature.ImageURL,
		})
	}

	return resp, nil
}
