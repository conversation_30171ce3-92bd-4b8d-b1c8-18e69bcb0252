package sitereportdailyreportaddition

import "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"

type SiteReportDailyReportAddition struct {
	ID                     int64  `gorm:"column:id;primary_key"`
	SiteReportID           int64  `gorm:"column:site_report_id"`
	DailyReportAdditionID  int64  `gorm:"column:daily_report_addition_id"`
	Snapshot               string `gorm:"column:snapshot;type:json"`

	// Foreign key relationship for daily report addition details
	DailyReportAddition dailyreportaddition.DailyReportAddition `gorm:"foreignkey:DailyReportAdditionID"`
}

type Snapshot struct {
	Title           string  `json:"title"`
	AmountPerSite   float64 `json:"amount_per_site"`
	AmountPerWorker float64 `json:"amount_per_worker"`
}

// DailyReportAdditionItem represents a daily report addition item for bulk creation
type DailyReportAdditionItem struct {
	DailyReportAdditionID int64
	Snapshot              string
}

// BulkCreateParam represents the parameters for bulk creating site report daily report additions
type BulkCreateParam struct {
	SiteReportID            int64
	DailyReportAdditions    []DailyReportAdditionItem
}

// GetBySiteReportIDParam represents the parameters for getting site report daily report additions by site report ID
type GetBySiteReportIDParam struct {
	SiteReportID int64
}

// DailyReportAdditionUpdateData represents a daily report addition item for bulk update
type DailyReportAdditionUpdateData struct {
	DailyReportAdditionID int64
	Snapshot              string
}

// BulkUpdateParam represents the parameters for bulk updating site report daily report additions
type BulkUpdateParam struct {
	SiteReportID         int64
	DailyReportAdditions []DailyReportAdditionUpdateData
}
