package signature

import (
	"time"
)

type Signature struct {
	ID        int64      `gorm:"column:id;primary_key"`
	Code      string     `gorm:"column:code;unique"`
	Name      string     `gorm:"column:name"`
	IsPic     bool       `gorm:"column:is_pic"`
	IsBoss    bool       `gorm:"column:is_boss"`
	ImageURL  string     `gorm:"column:image_url"`
	CreatedAt time.Time  `gorm:"column:created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"`
}

type GetListParam struct {
	Type string `query:"type"`
}

// GetListResp represents the response structure for signature list
type GetListResp struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	ImageURL string `json:"image_url"`
}
