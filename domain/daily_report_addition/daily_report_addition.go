package dailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DailyReportAdditionDomainItf interface {
		GetByIDs(ctx context.Context, param GetByIDsParam) ([]DailyReportAddition, error)
		GetList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error)
	}

	DailyReportAdditionResourceItf interface {
		getByIDs(ctx context.Context, param GetByIDsParam) ([]DailyReportAddition, error)
		getList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error)
	}
)

// GetByIDs retrieves daily report addition records by IDs.
func (d *DailyReportAdditionDomain) GetByIDs(ctx context.Context, param GetByIDsParam) ([]DailyReportAddition, error) {
	additions, err := d.resource.getByIDs(ctx, param)
	if err != nil {
		return []DailyReportAddition{}, log.LogError(err, nil)
	}
	return additions, nil
}

// GetList retrieves daily report addition records with optional search filtering.
func (d *DailyReportAdditionDomain) GetList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error) {
	additions, err := d.resource.getList(ctx, param)
	if err != nil {
		return []DailyReportAddition{}, log.LogError(err, nil)
	}
	return additions, nil
}
