package utils

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"slices"
	"strconv"
	"strings"
	"time"
)

// hasRole checks if the user has at least one of the specified roles.
// targetRoles is variadic, making the function flexible to accept one or more roles.
func HasRole(userRoles []string, targetRoles ...string) bool {
	for _, r := range targetRoles {
		if slices.Contains(userRoles, r) {
			return true
		}
	}
	return false
}

// SortStrings sorts a slice of strings in ascending order
func SortStrings(slice []string) {
	for i := 0; i < len(slice)-1; i++ {
		for j := i + 1; j < len(slice); j++ {
			if slice[i] > slice[j] {
				slice[i], slice[j] = slice[j], slice[i]
			}
		}
	}
}

// SplitStringToInt64 splits a comma-separated string of numbers into a slice of int64
func SplitStringToInt64(s string) []int64 {
	if s == "" {
		return []int64{}
	}

	// Split the string by comma
	parts := strings.Split(s, ",")
	if len(parts) == 0 {
		return []int64{}
	}

	// Convert each part to int64
	result := make([]int64, len(parts))
	for i, part := range parts {
		result[i], _ = strconv.ParseInt(part, 10, 64)
	}

	return result
}

// FormatJapaneseDate formats a time.Time to Japanese date format (e.g., "2025年6月7日")
func FormatJapaneseDate(t time.Time) string {
	return fmt.Sprintf("%d年%d月%d日", t.Year(), t.Month(), t.Day())
}

// ReadMultipartFile reads a multipart file and returns an io.ReadSeeker
func ReadMultipartFile(multipartFile *multipart.FileHeader) (io.ReadSeeker, error) {
	file, err := multipartFile.Open()
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var buf bytes.Buffer
	_, err = io.Copy(&buf, file)
	if err != nil {
		return nil, err
	}

	imageReader := bytes.NewReader(buf.Bytes())

	return imageReader, nil
}
