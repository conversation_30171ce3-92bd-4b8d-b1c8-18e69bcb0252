package report

import (
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetDailyReportAdditionTable configures GoAdmin CRUD for the daily_report_addition table.
func GetDailyReportAdditionTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("加算額コード", "code", db.Varchar)
	info.AddField("表示タイトル", "title", db.Varchar)
	info.AddField("説明", "explanation", db.Text)
	info.AddField("加算額(1現場あたり)", "amount_per_site", db.Float).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	info.AddField("加算額(1時間あたり)", "amount_per_hour", db.Float).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	info.AddField("法定福利対象", "welfare_subject", db.Varchar)
	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})

	info.SetTable("daily_report_addition").SetTitle("日報加算額パターン")

	// Soft delete logic
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("daily_report_addition").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude deleted
	info.WhereRaw("\"daily_report_addition\".deleted_at IS NULL")

	// Form
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("加算額コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("表示タイトル", "title", db.Varchar, form.Text).FieldMust()
	formList.AddField("説明", "explanation", db.Text, form.RichText).FieldMust()
	formList.AddField("加算額(1現場あたり)", "amount_per_site", db.Float, form.Number).FieldMust().FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	formList.AddField("加算額(1時間あたり)", "amount_per_hour", db.Float, form.Number).FieldMust().FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	formList.AddField("法定福利対象", "welfare_subject", db.Varchar, form.Text).FieldMust()

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("daily_report_addition").SetTitle("日報加算額パターン")

	return tbl
}
