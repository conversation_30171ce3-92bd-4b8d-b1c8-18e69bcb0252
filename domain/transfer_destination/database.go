package transferdestination

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getByIDs fetches transfer destinations by IDs and returns a map for efficient lookup.
func (rsc TransferDestinationResource) getByIDs(ctx context.Context, param GetByIDsParam) (map[int64]TransferDestination, error) {
	var transferDestinations []TransferDestination

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id IN ?", param.IDs).
		Where("deleted_at IS NULL").
		Find(&transferDestinations).Error

	if err != nil {
		return nil, log.LogError(err, nil)
	}

	// Create map for efficient lookup
	transferDestinationMap := make(map[int64]TransferDestination)
	for _, td := range transferDestinations {
		transferDestinationMap[td.ID] = td
	}

	return transferDestinationMap, nil
}
