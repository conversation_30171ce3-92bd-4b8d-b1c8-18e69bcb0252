package sitereportdailyreportaddition

import (
	"context"
	"fmt"
	"strings"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// bulkCreateWithTx creates multiple site report daily report addition records within a transaction
func (rsc SiteReportDailyReportAdditionResource) bulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error {
	if len(param.DailyReportAdditions) == 0 {
		return nil
	}

	// Prepare records for bulk insert
	records := make([]SiteReportDailyReportAddition, 0, len(param.DailyReportAdditions))
	for _, item := range param.DailyReportAdditions {
		records = append(records, SiteReportDailyReportAddition{
			SiteReportID:          param.SiteReportID,
			DailyReportAdditionID: item.DailyReportAdditionID,
			Snapshot:              item.Snapshot,
		})
	}

	// Use transaction if provided, otherwise use default DB
	db := tx
	if db == nil {
		db = dbmanager.Manager().WithContext(ctx)
	}

	err := db.Create(&records).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getBySiteReportIDWithTx retrieves site report daily report additions by site report ID within a transaction
func (rsc SiteReportDailyReportAdditionResource) getBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportDailyReportAddition, error) {
	var additions []SiteReportDailyReportAddition

	// Use transaction if provided, otherwise use default DB
	db := tx
	if db == nil {
		db = dbmanager.Manager().WithContext(ctx)
	}

	err := db.Where("site_report_id = ?", param.SiteReportID).
		Preload("DailyReportAddition").
		Find(&additions).Error

	if err != nil {
		return []SiteReportDailyReportAddition{}, log.LogError(err, nil)
	}

	return additions, nil
}

// bulkUpdateWithTx updates multiple site report daily report addition records within a transaction
func (rsc SiteReportDailyReportAdditionResource) bulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error {
	if len(param.DailyReportAdditions) == 0 {
		return nil
	}

	// Use transaction if provided, otherwise use default DB
	db := tx
	if db == nil {
		db = dbmanager.Manager().WithContext(ctx)
	}

	// Build CASE statement for bulk update
	var caseStatements []string
	var args []interface{}

	for _, item := range param.DailyReportAdditions {
		caseStatements = append(caseStatements, "WHEN daily_report_addition_id = ? THEN ?::json")
		args = append(args, item.DailyReportAdditionID, item.Snapshot)
	}

	// Extract daily report addition IDs for WHERE clause
	var dailyReportAdditionIDs []int64
	for _, item := range param.DailyReportAdditions {
		dailyReportAdditionIDs = append(dailyReportAdditionIDs, item.DailyReportAdditionID)
	}

	// Build and execute the bulk update query
	query := fmt.Sprintf(`
		UPDATE site_report_daily_report_addition 
		SET snapshot = CASE %s END 
		WHERE site_report_id = ? AND daily_report_addition_id IN (%s)`,
		strings.Join(caseStatements, " "),
		strings.Repeat("?,", len(dailyReportAdditionIDs)-1)+"?")

	// Add site_report_id and daily_report_addition_ids to args
	args = append(args, param.SiteReportID)
	for _, id := range dailyReportAdditionIDs {
		args = append(args, id)
	}

	err := db.Exec(query, args...).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// bulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx deletes site report daily report additions by site report ID and daily report addition IDs within a transaction
func (rsc SiteReportDailyReportAdditionResource) bulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx(ctx context.Context, tx *gorm.DB, siteReportID int64, dailyReportAdditionIDs []int64) error {
	if len(dailyReportAdditionIDs) == 0 {
		return nil
	}

	// Use transaction if provided, otherwise use default DB
	db := tx
	if db == nil {
		db = dbmanager.Manager().WithContext(ctx)
	}

	err := db.Where("site_report_id = ? AND daily_report_addition_id IN ?", siteReportID, dailyReportAdditionIDs).
		Delete(&SiteReportDailyReportAddition{}).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}
