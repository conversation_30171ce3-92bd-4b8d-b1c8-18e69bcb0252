package config

import (
	"fmt"

	goAdmin "github.com/Sera-Global/be-nbs-accounting-system/server/admin"

	"github.com/GoAdminGroup/go-admin/engine"
	"github.com/GoAdminGroup/go-admin/examples/datamodel"
	"github.com/GoAdminGroup/go-admin/modules/config"
	"github.com/GoAdminGroup/go-admin/modules/language"
	"github.com/GoAdminGroup/go-admin/plugins/admin"
	"github.com/GoAdminGroup/go-admin/plugins/example"

	_ "github.com/GoAdminGroup/go-admin/adapter/echo"                // Import the adapter, it must be imported. If it is not imported, you need to define it yourself.
	_ "github.com/GoAdminGroup/go-admin/modules/db/drivers/postgres" // Import the sql driver
	_ "github.com/GoAdminGroup/themes/adminlte"                      // Import the theme
)

var eng *engine.Engine

func InitGoAdmin() {
	appCfg := GetConfig()

	// Instantiate a GoAdmin engine object.
	eng = engine.Default()

	// GoAdmin global configuration, can also be imported as a json file.
	cfg := config.Config{
		Databases: config.DatabaseList{
			"default": config.Database{
				Dsn:    fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", appCfg.DBUser, appCfg.DBPassword, appCfg.DBHost, appCfg.DBPort, appCfg.DBName),
				Driver: config.DriverPostgresql,
			},
		},
		UrlPrefix: "admin", // The url prefix of the website.
		// Store must be set and guaranteed to have write access, otherwise new administrator users cannot be added.
		Store: config.Store{
			Path:   "../../files",
			Prefix: "uploads",
		},
		Language: language.JP,
	}

	adminPlugin := admin.NewAdmin(datamodel.Generators)
	examplePlugin := example.NewExample()

	// Add configuration and plugins, use the Use method to mount to the web framework.
	_ = eng.AddConfig(&cfg).
		AddPlugins(adminPlugin, examplePlugin).
		AddGenerators(goAdmin.Generators)

	fmt.Println("GoAdmin initialized")
}

func GoAdminManager() *engine.Engine {
	return eng
}
