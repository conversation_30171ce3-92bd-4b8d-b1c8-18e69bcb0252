package customer

import (
	"strconv"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetDepartmentPICTable configures GoAdmin CRUD for the department_pic table.
func GetDepartmentPICTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()

	info.AddField("部署", "department_id", db.Bigint).FieldDisplay(func(field types.FieldModel) interface{} {
		var deptName string
		if gormDB != nil {
			_ = gormDB.Table("department").Select("name").Where("id = ?", field.Value).Scan(&deptName).Error
		}
		if deptName == "" {
			return field.Value
		}
		return deptName
	})

	info.AddField("担当者所属", "pic_belong", db.Varchar)
	info.AddField("担当者名", "pic_name", db.Varchar)
	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})

	info.SetTable("department_pic").SetTitle("部署PIC一覧")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("department_pic").Where("id IN ?", ids).Delete(&map[string]interface{}{}).Error
	})

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("部署", "department_id", db.Bigint, form.SelectSingle).
		FieldOptions(departmentOptions())
	formList.AddField("担当者所属", "pic_belong", db.Varchar, form.Text).FieldMust()
	formList.AddField("担当者名", "pic_name", db.Varchar, form.Text).FieldMust()

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("department_pic").SetTitle("部署PIC")

	return tbl
}

// departmentOptions builds select options for active departments
func departmentOptions() types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}

	var items []struct {
		ID   int
		Name string
	}
	db.Table("department").Select("id, name").Where("deleted_at IS NULL").Find(&items)
	opts := make(types.FieldOptions, 0, len(items))
	for _, d := range items {
		opts = append(opts, types.FieldOption{
			Text:  d.Name,
			Value: strconv.Itoa(d.ID),
		})
	}

	return opts
}
