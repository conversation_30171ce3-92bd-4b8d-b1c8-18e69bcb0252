package customer

import (
	"strconv"
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetDepartmentTable configures GoAdmin CRUD for the department table.
func GetDepartmentTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("部署コード", "code", db.Varchar)
	info.AddField("顧客名", "customer_id", db.Bigint).FieldDisplay(func(field types.FieldModel) interface{} {
		var customerName string
		if gormDB != nil {
			_ = gormDB.Table("customer").Select("name").Where("id = ?", field.Value).Scan(&customerName).Error
		}
		if customerName == "" {
			return field.Value
		}
		return customerName
	})
	info.AddField("部署名", "name", db.Varchar)
	info.AddField("振込先情報", "transfer_destination_id", db.Bigint).FieldDisplay(func(field types.FieldModel) interface{} {
		var code string
		if gormDB != nil {
			_ = gormDB.Table("transfer_destination").Select("code").Where("id = ?", field.Value).Scan(&code).Error
		}
		if code == "" {
			return field.Value
		}
		return code
	})
	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})

	info.SetTable("department").SetTitle("部署一覧")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("department").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft deleted
	info.WhereRaw("\"department\".deleted_at IS NULL")

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("部署コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("顧客名", "customer_id", db.Bigint, form.SelectSingle).
		FieldOptions(customerOptions())
	formList.AddField("部署名", "name", db.Varchar, form.Text).FieldMust()
	formList.AddField("振込先情報", "transfer_destination_id", db.Bigint, form.SelectSingle).
		FieldOptions(transferDestinationOptions())

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("department").SetTitle("部署")

	return tbl
}

// customerOptions builds dropdown options for active customers (deleted_at IS NULL).
func customerOptions() types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}
	var items []struct {
		ID   int
		Name string
	}
	db.Table("customer").Select("id, name").Where("deleted_at IS NULL").Find(&items)
	opts := make(types.FieldOptions, 0, len(items))
	for _, c := range items {
		opts = append(opts, types.FieldOption{
			Text:  c.Name,
			Value: strconv.Itoa(c.ID),
		})
	}
	return opts
}

// transferDestinationOptions builds dropdown options for active transfer destinations (deleted_at IS NULL).
func transferDestinationOptions() types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}
	var items []struct {
		ID   int
		Code string
	}
	db.Table("transfer_destination").Select("id, code").Where("deleted_at IS NULL").Find(&items)
	opts := make(types.FieldOptions, 0, len(items))
	for _, t := range items {
		opts = append(opts, types.FieldOption{
			Text:  t.Code,
			Value: strconv.Itoa(t.ID),
		})
	}
	return opts
}
