package customer

import (
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetStatutoryTable configures GoAdmin CRUD for the statutory table.
func GetStatutoryTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("法定福利計算コード", "code", db.Varchar)
	info.AddField("タイトル", "title", db.Varchar)
	info.AddField("法定福利支援金率", "rate", db.Float)
	info.AddField("加算額(1現場あたり)", "addamount_per_site", db.Float).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	info.AddField("加算額(1時間あたり)", "addamount_per_hour", db.Float).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(field types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(field.Value)
	})

	info.SetTable("statutory").SetTitle("法定福利支援金計算")

	// Soft delete
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("statutory").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude deleted
	info.WhereRaw("\"statutory\".deleted_at IS NULL")

	// Form
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("法定福利計算コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("タイトル", "title", db.Varchar, form.Text).FieldMust()
	formList.AddField("法定福利支援金率", "rate", db.Float, form.Number).FieldMust()
	formList.AddField("加算額(1現場あたり)", "addamount_per_site", db.Float, form.Number).FieldMust().FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	formList.AddField("加算額(1時間あたり)", "addamount_per_hour", db.Float, form.Number).FieldMust().FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("statutory").SetTitle("法定福利支援金計算")

	return tbl
}
