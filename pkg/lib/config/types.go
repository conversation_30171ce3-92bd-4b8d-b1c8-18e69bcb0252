package config

type Configuration struct {
	Env  string `env:"ENV"`
	Port string `env:"PORT"`

	DBHost     string `env:"DB_HOST"`
	DBPort     string `env:"DB_PORT"`
	DBName     string `env:"DB_NAME"`
	DBUser     string `env:"DB_USER"`
	DBPassword string `env:"DB_PASSWORD"`

	JWTSecret string `env:"JWT_SECRET"`

	AwsBaseUrl      string `env:"AWS_BASE_URL"`
	AwsRegion       string `env:"AWS_REGION"`
	AwsS3Bucket     string `env:"AWS_S3_BUCKET"`
	AwsAccessKey    string `env:"AWS_ACCESS_KEY"`
	AwsAccessSecret string `env:"AWS_ACCESS_SECRET"`
}
