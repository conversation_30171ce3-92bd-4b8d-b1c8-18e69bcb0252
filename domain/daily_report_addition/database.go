package dailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getByIDs fetches daily report addition records by IDs.
func (rsc DailyReportAdditionResource) getByIDs(ctx context.Context, param GetByIDsParam) ([]DailyReportAddition, error) {
	var additions []DailyReportAddition

	if len(param.IDs) == 0 {
		return additions, nil
	}

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id IN ?", param.IDs).
		Order("title ASC").
		Find(&additions).Error

	if err != nil {
		return []DailyReportAddition{}, log.LogError(err, nil)
	}

	return additions, nil
}

// getList fetches all daily report additions ordered by title.
func (rsc DailyReportAdditionResource) getList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error) {
	var additions []DailyReportAddition

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&additions).Error

	if err != nil {
		return []DailyReportAddition{}, log.LogError(err, nil)
	}

	return additions, nil
}
