package sitereport

import (
	"time"

	consumptiontaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/consumption_tax"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	sitereportoptionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_option"
	sitereportworkerqualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker_qualification"
)

// GetListReq represents the request parameters for getting site report list
type GetListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`

	ParsedStartDate time.Time
	ParsedEndDate   time.Time
}

// GetListResp represents the response structure for site report list
type GetListResp struct {
	Year  string      `json:"year"`
	Month []MonthData `json:"month"`
}

type MonthData struct {
	Value       string     `json:"value"`
	Worker      int64      `json:"worker"`
	TotalAmount float64    `json:"total_amount"`
	Date        []DateData `json:"date"`
}

type DateData struct {
	Value       string       `json:"value"`
	Worker      int64        `json:"worker"`
	TotalAmount float64      `json:"total_amount"`
	Report      []ReportData `json:"report"`
}

type ReportData struct {
	SiteReportID int64   `json:"site_report_id"`
	SiteName     string  `json:"site_name"`
	Worker       int64   `json:"worker"`
	TotalAmount  float64 `json:"total_amount"`
	BStartTime   string  `json:"b_start_time"`
	BEndTime     string  `json:"b_end_time"`
	Note         string  `json:"note"`
	IsLocked     bool    `json:"is_locked"`
}

// GetInvoiceListReq represents the request parameters for getting site report invoice list
type GetInvoiceListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`

	ParsedStartDate time.Time
	ParsedEndDate   time.Time
}

// GetInvoiceListResp represents the response structure for site report invoice list
type GetInvoiceListResp struct {
	TotalBillingAmount float64                      `json:"total_billing_amount"`
	TotalInvoice       int                          `json:"total_invoice"`
	Customer           []GetInvoiceListCustomerResp `json:"customer"`
}

type GetInvoiceListCustomerResp struct {
	Name    string                      `json:"name"`
	Invoice []GetInvoiceListInvoiceResp `json:"invoice"`
}

type GetInvoiceListInvoiceResp struct {
	SiteReportID    int64  `json:"site_report_id"`
	DepartmentName  string `json:"department_name"`
	PicName         string `json:"pic_name"`
	WorkDate        string `json:"work_date"`
	BillDate        string `json:"bill_date"`
	SiteName        string `json:"site_name"`
	TotalWorker     int64  `json:"total_worker"`
	BStartTime      string `json:"b_start_time"`
	BEndTime        string `json:"b_end_time"`
	IsLocked        bool   `json:"is_locked"`
	IsInvoiceIssued bool   `json:"is_invoice_issued"`
}

// GetInvoiceDetailReq represents the request parameters for getting invoice detail
type GetInvoiceDetailReq struct {
	SiteReportIDs string `query:"site_report_ids"`

	ParsedSiteReportIDs []int64
}

// GetStatutoryDetailReq represents the request parameters for getting statutory detail
type GetStatutoryDetailReq struct {
	SiteReportIDs string `query:"site_report_ids"`

	ParsedSiteReportIDs []int64
}

// GetInvoiceDetailResp represents the response structure for invoice detail
type GetInvoiceDetailResp struct {
	Invoices []InvoiceDetailResp `json:"invoices"`
}

type InvoiceDetailResp struct {
	Customer            CustomerDetailResp            `json:"customer"`
	DefaultAddress      string                        `json:"default_address"`
	TransferDestination TransferDestinationDetailResp `json:"transfer_destination"`
	IssuedDate          string                        `json:"issued_date"`
	StartDate           string                        `json:"start_date"`
	EndDate             string                        `json:"end_date"`
	Amount              float64                       `json:"amount"`
	ConsumptionTax      ConsumptionTaxDetailResp      `json:"consumption_tax"`
	TotalAmount         float64                       `json:"total_amount"`
	Detail              []InvoiceDetailItemResp       `json:"detail"`
}

type CustomerDetailResp struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	PostCode   string `json:"post_code"`
	Prefecture string `json:"prefecture"`
	Address    string `json:"address"`
}

type TransferDestinationDetailResp struct {
	BankName      string `json:"bank_name"`
	Branch        string `json:"branch"`
	AccountNumber string `json:"account_number"`
}

type ConsumptionTaxDetailResp struct {
	Rate   float64 `json:"rate"`
	Amount float64 `json:"amount"`
}

type InvoiceDetailItemResp struct {
	SiteReportID      int64   `json:"site_report_id"`
	WorkDate          string  `json:"work_date"`
	SiteName          string  `json:"site_name"`
	NoteForInvoice    string  `json:"note_for_invoice"`
	PicName           string  `json:"pic_name"`
	DistrictBlockName string  `json:"district_block_name"`
	Quantity          string  `json:"quantity"`
	TotalAmount       float64 `json:"total_amount"`
}

// GetStatutoryDetailResp represents the response structure for statutory detail
type GetStatutoryDetailResp struct {
	Data []StatutoryDetailResp `json:"data"`
}

type StatutoryDetailResp struct {
	Customer             StatutoryDetailCustomerResp `json:"customer"`
	DefaultAddress       string                      `json:"default_address"`
	StartDate            string                      `json:"start_date"`
	EndDate              string                      `json:"end_date"`
	Detail               []StatutoryDetailItemResp   `json:"detail"`
	TotalTargetWorker    int64                       `json:"total_target_worker"`
	TotalStatutoryAmount float64                     `json:"total_statutory_amount"`
}

type StatutoryDetailCustomerResp struct {
	ID             int64   `json:"id"`
	Name           string  `json:"name"`
	DepartmentName string  `json:"department_name"`
	PostCode       string  `json:"post_code"`
	Prefecture     string  `json:"prefecture"`
	Address        string  `json:"address"`
	StatutoryRate  float64 `json:"statutory_rate"`
}

type StatutoryDetailItemResp struct {
	SiteReportID      int64   `json:"site_report_id"`
	SiteName          string  `json:"site_name"`
	WorkerName        string  `json:"worker_name"`
	DistrictBlockName string  `json:"district_block_name"`
	TotalWorker       int64   `json:"total_worker"`
	ExpensePerWorker  float64 `json:"expense_per_worker"`
	TargetWorker      int64   `json:"target_worker"`
	AddFeePerWorker   float64 `json:"add_fee_per_worker"`
	AddFeePerSite     float64 `json:"add_fee_per_site"`
	Adjustment        float64 `json:"adjustment"`
	StatutoryAmount   float64 `json:"statutory_amount"`
}

type BuildInvoiceForCustomerParam struct {
	Reports                []sitereportDmn.SiteReport
	ConsumptionTax         consumptiontaxDmn.ConsumptionTax
	TransferDestinationMap map[int64]TransferDestinationDetailResp
}

// GetStatutoryListReq represents the request parameters for getting site report statutory list
type GetStatutoryListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`

	ParsedStartDate time.Time
	ParsedEndDate   time.Time
}

// GetStatutoryListResp represents the response structure for site report statutory list
type GetStatutoryListResp struct {
	TotalStatutoryAmount float64                        `json:"total_statutory_amount"`
	TotalStatutoryReport int                            `json:"total_statutory_report"`
	Customer             []GetStatutoryListCustomerResp `json:"customer"`
}

type GetStatutoryListCustomerResp struct {
	Name      string                          `json:"name"`
	Statutory []GetStatutoryListStatutoryResp `json:"statutory"`
}

type GetStatutoryListStatutoryResp struct {
	SiteReportID          int64  `json:"site_report_id"`
	SiteReportStatutoryID int64  `json:"site_report_statutory_id"`
	DepartmentName        string `json:"department_name"`
	PicName               string `json:"pic_name"`
	WorkDate              string `json:"work_date"`
	BillDate              string `json:"bill_date"`
	SiteName              string `json:"site_name"`
	TotalWorker           int64  `json:"total_worker"`
	BStartTime            string `json:"b_start_time"`
	BEndTime              string `json:"b_end_time"`
	IsLocked              bool   `json:"is_locked"`
	IsStatutoryIssued     bool   `json:"is_statutory_issued"`
}

// BulkUpdateReq represents the request structure for bulk updating site reports
type BulkUpdateReq struct {
	SiteReportIDs   []int64 `json:"site_report_ids"`
	WorkDate        *string `json:"work_date,omitempty"`         // Optional, format: YYYY-MM-DD
	IsLocked        *bool   `json:"is_locked,omitempty"`         // Optional boolean
	IsInvoiceIssued *bool   `json:"is_invoice_issued,omitempty"` // Optional boolean

	UserRoles []string
}

// BulkUpdateStatutoryReq represents the request structure for bulk updating site report statutory status
type BulkUpdateStatutoryReq struct {
	SiteReportStatutoryIDs []int64 `json:"site_report_statutory_ids"` // Required array of integers
	IsStatutoryIssued      bool    `json:"is_statutory_issued"`       // Required boolean
}

// BulkUpdateWorkerReq represents the request structure for bulk updating site report worker payment status
type BulkUpdateWorkerReq struct {
	SiteReportWorkerIDs []int64 `json:"site_report_worker_ids"` // Required array of integers
	IsPaid              bool    `json:"is_paid"`                // Required boolean
}

// GetWorkerReportListReq represents the request parameters for getting worker report list
type GetWorkerReportListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`

	ParsedStartDate time.Time
	ParsedEndDate   time.Time
}

// GetWorkerReportListResp represents the response structure for worker report list
type GetWorkerReportListResp []GetWorkerReportListWorkerResp

// GetWorkerReportListWorkerResp represents a worker with their reports
type GetWorkerReportListWorkerResp struct {
	WorkerName string                          `json:"worker_name"`
	Report     []GetWorkerReportListReportResp `json:"report"`
}

// GetWorkerReportListReportResp represents a single worker report
type GetWorkerReportListReportResp struct {
	SiteReportID       int64  `json:"site_report_id"`
	SiteReportWorkerID int64  `json:"site_report_worker_id"`
	SiteName           string `json:"site_name"`
	WorkDate           string `json:"work_date"`
	StartTime          string `json:"start_time"`
	EndTime            string `json:"end_time"`
	Status             string `json:"status"`
	IssuedDate         string `json:"issued_date"`
}

// GetWorkerPaymentStatementListReq represents the request parameters for getting worker payment statement list
type GetWorkerPaymentStatementListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`

	ParsedStartDate time.Time
	ParsedEndDate   time.Time
}

// GetWorkerPaymentStatementListResp represents the response structure for worker payment statement list
type GetWorkerPaymentStatementListResp struct {
	TotalPaymentAmount    float64                                 `json:"total_payment_amount"`
	TotalPaymentStatement int                                     `json:"total_payment_statement"`
	PaymentStatement      []GetWorkerPaymentStatementListItemResp `json:"payment_statement"`
}

// GetWorkerPaymentStatementListItemResp represents a single payment statement item
type GetWorkerPaymentStatementListItemResp struct {
	SiteReportID       int64   `json:"site_report_id"`
	SiteReportWorkerID int64   `json:"site_report_worker_id"`
	WorkerID           int64   `json:"worker_id"`
	WorkerName         string  `json:"worker_name"`
	WorkDate           string  `json:"work_date"`
	DebtPayment        float64 `json:"debt_payment"`
	TaxableSalary      float64 `json:"taxable_salary"`
	TaxFreeSalary      float64 `json:"tax_free_salary"`
	TaxAmount          float64 `json:"tax_amount"`
	TotalPayment       float64 `json:"total_payment"`
}

// GetStatutoryCalculationVariableReq represents the request parameters for getting statutory calculation variables
type GetStatutoryCalculationVariableReq struct {
	CustomerID             int64  `query:"customer_id"`
	BasicPriceID           int64  `query:"basic_price_id"`
	StartTime              string `query:"start_time"`
	EndTime                string `query:"end_time"`
	BreakTime              string `query:"break_time"`
	DailyReportAdditionIDs string `query:"daily_report_addition_id"` // multiple ids separated by comma

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

// GetStatutoryCalculationVariableResp represents the response structure for statutory calculation variables
type GetStatutoryCalculationVariableResp struct {
	StatutoryRate    float64 `json:"statutory_rate"`
	ExpensePerWorker float64 `json:"expense_per_worker"`
	AddFeePerSite    float64 `json:"add_fee_per_site"`
	AddFeePerWorker  float64 `json:"add_fee_per_worker"`
}

type PriceItem struct {
	Hour  string  `json:"hour"`
	Price float64 `json:"price"`
}

type CalculateExpensePerWorker struct {
	BasicPriceID int64
	StartTime    time.Time
	EndTime      time.Time
	BreakTime    *time.Time
}

// GetDetailListReq represents the request parameters for getting site report detail list
type GetDetailListReq struct {
	WorkDate string `query:"work_date"`
	ID       int64  `query:"id"`

	ParsedWorkDate time.Time
}

// GetDetailListResp represents the response structure for site report detail list
type GetDetailListResp struct {
	ID                           int64                                  `json:"id"`
	WorkDate                     string                                 `json:"work_date"`
	IsLocked                     bool                                   `json:"is_locked"`
	SiteName                     string                                 `json:"site_name"`
	CustomerID                   int64                                  `json:"customer_id"`
	CustomerName                 string                                 `json:"customer_name"`
	ReportNumber                 string                                 `json:"report_number"`
	HasStatutory                 bool                                   `json:"has_statutory"`
	DepartmentID                 int64                                  `json:"department_id"`
	DepartmentName               string                                 `json:"department_name"`
	DepartmentPicID              int64                                  `json:"department_pic_id"`
	DepartmentPicName            string                                 `json:"department_pic_name"`
	BillDate                     string                                 `json:"bill_date"`
	BasicPriceID                 int64                                  `json:"basic_price_id"`
	BasicPriceName               string                                 `json:"basic_price_name"`
	DailyReportAdditionID        int64                                  `json:"daily_report_addition_id"`
	DailyReportAdditionName      string                                 `json:"daily_report_addition_name"`
	DailyReportAdditionPerSite   float64                                `json:"daily_report_addition_per_site"`
	DailyReportAdditionPerWorker float64                                `json:"daily_report_addition_per_worker"`
	TotalWorker                  int64                                  `json:"total_worker"`
	DistrictBlockID              int64                                  `json:"district_block_id"`
	DistrictBlockName            string                                 `json:"district_block_name"`
	DistrictBlockUnit            int64                                  `json:"district_block_unit"`
	TransitPlaceBlockID          int64                                  `json:"transit_place_block_id"`
	TransitPlaceBlockName        string                                 `json:"transit_place_block_name"`
	TransitPlaceBlockUnit        int64                                  `json:"transit_place_block_unit"`
	BStartTime                   string                                 `json:"b_start_time"`
	BEndTime                     string                                 `json:"b_end_time"`
	SStartTime                   string                                 `json:"s_start_time"`
	SEndTime                     string                                 `json:"s_end_time"`
	BreakTime                    string                                 `json:"break_time"`
	LateEarlyWorker              int64                                  `json:"late_early_worker"`
	ExtraTimeCharge              []ExtraTimeChargeItem                  `json:"extra_time_charge"`
	Note                         string                                 `json:"note"`
	NoteForInvoice               string                                 `json:"note_for_invoice"`
	TotalAmount                  float64                                `json:"total_amount"`
	Statutory                    *GetDetailListStatutoryResp            `json:"statutory"`
	Option                       []GetDetailListOptionResp              `json:"option"`
	DailyReportAdditions         []GetDetailListDailyReportAdditionResp `json:"daily_report_additions"`
	Worker                       []GetDetailListWorkerResp              `json:"worker"`
}

type ExtraTimeChargeItem struct {
	Time        string `json:"time"`
	TotalWorker int64  `json:"total_worker"`
}

type GetDetailListStatutoryResp struct {
	ID               int64   `json:"id"`
	Rate             float64 `json:"rate"`
	ExpensePerWorker float64 `json:"expense_per_worker"`
	Adjustment       float64 `json:"adjustment"`
	TotalAmount      float64 `json:"total_amount"`
	Note             string  `json:"note"`
}

type GetDetailListOptionResp struct {
	ID       int64   `json:"id"`
	OptionID int64   `json:"option_id"`
	Name     string  `json:"name"`
	Amount   float64 `json:"amount"`
}

type GetDetailListDailyReportAdditionResp struct {
	ID                    int64   `json:"id"`
	DailyReportAdditionID int64   `json:"daily_report_addition_id"`
	Title                 string  `json:"title"`
	AmountPerSite         float64 `json:"amount_per_site"`
	AmountPerWorker       float64 `json:"amount_per_worker"`
}

type GetDetailListWorkerResp struct {
	ID               int64           `json:"id"`
	WorkerID         int64           `json:"worker_id"`
	Name             string          `json:"name"`
	StartTime        string          `json:"start_time"`
	EndTime          string          `json:"end_time"`
	BreakTime        string          `json:"break_time"`
	TransportExpense float64         `json:"transport_expense"`
	LeaderAllowance  float64         `json:"leader_allowance"`
	DistantFeeID     int64           `json:"distant_fee_id"`
	DistantFeeName   string          `json:"distant_fee_name"`
	Qualifications   []Qualification `json:"qualifications"`
}

// WorkerCalculationParam represents the request parameters for worker calculation
type WorkerCalculationParam struct {
	BasicPriceID              int64   `json:"basic_price_id"`
	WorkerID                  int64   `json:"worker_id"`
	StartTime                 string  `json:"start_time"`
	EndTime                   string  `json:"end_time"`
	BreakTime                 string  `json:"break_time"`
	TransportExpense          float64 `json:"transport_expense"`
	LeaderAllowance           float64 `json:"leader_allowance"`
	DistantFeeID              int64   `json:"distant_fee_id"`
	QualificationAllowanceIDs []int64 `json:"qualification_allowance_ids"`

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

// WorkerCalculationResp represents the response structure for worker calculation
type WorkerCalculationResp struct {
	TotalAmount         float64 `json:"total_amount"`
	TaxAmount           float64 `json:"tax_amount"`
	TotalAmountAfterTax float64 `json:"total_amount_after_tax"`
}

type DoWorkerCalculationResp struct {
	TotalAmount         float64
	TaxIncomeID         int64
	TaxAmount           float64
	TotalAmountAfterTax float64
	DistantFee          float64
	Qualifications      []Qualification
}

type CalculateQualificationFeeResp struct {
	TotalFee       float64
	Qualifications []Qualification
}

type Qualification struct {
	ID              int64   `json:"id"`
	QualificationID int64   `json:"qualification_id"`
	Title           string  `json:"title"`
	AddClaim        float64 `json:"add_claim"`
}

type CalculateTaxAmountResp struct {
	IncomeTaxID int64
	Amount      float64
}

// SaveWorkerReq represents the request parameters for saving site report worker
type SaveWorkerReq struct {
	ID               int64                               `json:"id"`
	WorkerID         int64                               `json:"worker_id"`
	BasicPriceID     int64                               `json:"basic_price_id"`
	StartTime        string                              `json:"start_time"`
	EndTime          string                              `json:"end_time"`
	BreakTime        string                              `json:"break_time"`
	TransportExpense float64                             `json:"transport_expense"`
	LeaderAllowance  float64                             `json:"leader_allowance"`
	DistantFeeID     int64                               `json:"distant_fee_id"`
	Qualifications   []SaveSiteReportWorkerQualification `json:"qualifications"`

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

type DoUpdateWorkerParam struct {
	Req                SaveWorkerReq
	CalcResp           DoWorkerCalculationResp
	WorkerSnapshotJSON string
}

type ManageWorkerQualificationsParam struct {
	WorkerID                  int64
	RequestedQualificationIDs []int64
	CalculatedQualifications  []Qualification
}

type ProcessQualificationChangesParam struct {
	WorkerID         int64
	ExistingQualMap  map[int64]bool
	RequestedQualMap map[int64]bool
	QualDataMap      map[int64]Qualification
}

type ExecuteBulkQualificationOperationsParam struct {
	WorkerID int64
	ToUpdate []sitereportworkerqualificationDmn.QualificationUpdateData
	ToInsert []sitereportworkerqualificationDmn.QualificationData
	ToDelete []int64
}

type CalculateTaxAmountParam struct {
	TotalAmount float64
	WorkerID    int64
}

// SiteReportCalculationReq represents the request structure for site report calculation
type SiteReportCalculationReq struct {
	HasStatutory          bool                                   `json:"has_statutory"`
	DepartmentPicID       int64                                  `json:"department_pic_id"`
	BasicPriceID          int64                                  `json:"basic_price_id"`
	DailyReportAdditions  []GetDetailListDailyReportAdditionResp `json:"daily_report_additions,omitempty"`
	TotalWorker           int64                                  `json:"total_worker"`
	DistrictBlockID       int64                                  `json:"district_block_id"`
	DistrictBlockUnit     int64                                  `json:"district_block_unit"`
	TransitPlaceBlockID   *int64                                 `json:"transit_place_block_id,omitempty"`
	TransitPlaceBlockUnit *int64                                 `json:"transit_place_block_unit,omitempty"`
	BStartTime            string                                 `json:"b_start_time"`
	BEndTime              string                                 `json:"b_end_time"`
	BreakTime             *string                                `json:"break_time,omitempty"`
	LateEarlyWorker       *int64                                 `json:"late_early_worker,omitempty"`
	ExtraTimeCharge       []ExtraTimeChargeItem                  `json:"extra_time_charge,omitempty"`
	Statutory             StatutoryCalculationInput              `json:"statutory"`
	Option                []OptionCalculationInput               `json:"option"`

	// Parsed time fields (populated during validation)
	ParsedBStartTime time.Time  `json:"-"`
	ParsedBEndTime   time.Time  `json:"-"`
	ParsedBreakTime  *time.Time `json:"-"`
}

// StatutoryCalculationInput represents statutory calculation input
type StatutoryCalculationInput struct {
	Adjustment float64 `json:"adjustment"`
}

// OptionCalculationInput represents option calculation input
type OptionCalculationInput struct {
	OptionID int64 `json:"option_id"`
}

// SiteReportCalculationResp represents the response structure for site report calculation
type SiteReportCalculationResp struct {
	TotalAmount     float64 `json:"total_amount"`
	StatutoryAmount float64 `json:"statutory_amount"`
}

type CalculateTimeRangeParam struct {
	StartTime time.Time
	EndTime   time.Time
	BreakTime *time.Time
}

type CalculateDistrictBlockAmountParam struct {
	BlockID   int64
	BlockUnit int64
	TimeRange float64
}

type CalculateTransitPlaceBlockAmountParam struct {
	BlockID   *int64
	BlockUnit *int64
	TimeRange float64
}

type DoSiteReportCalculationResp struct {
	TotalAmount              float64
	StatutoryAmount          float64
	CustomerID               int64
	CustomerName             string
	DepartmentID             int64
	DepartmentName           string
	DepartmentPicName        string
	BasicPriceName           string
	AddFeePerWorker          float64
	AddFeePerSite            float64
	TotalDailyReportAddition float64
	DailyReportAdditionData  map[int64]DailyReportAdditionData
	DistrictBlockName        string
	DistrictBlockAmount      float64
	TransitPlaceBlockName    string
	TransitPlaceBlockAmount  float64
	ExpensePerWorker         float64
	StatutoryRate            float64
	OptionData               map[int64]OptionData
	ExtraTimeChargeData      map[string]ExtraTimeChargeItemData
}

type CalculateDistrictBlockAmountResp struct {
	Amount    float64
	BlockName string
}

type CalculateTransitPlaceBlockAmountResp struct {
	Amount    float64
	BlockName string
}

type CalculateAddFeesResp struct {
	AddFeePerSite   float64
	AddFeePerWorker float64
}

type CalculateExtraTimeChargeAmountParam struct {
	ExtraTimeCharges []ExtraTimeChargeItem
	BasicPriceID     int64
}

type CalculateExtraTimeChargeAmountResp struct {
	TotalAmount float64
	Data        map[string]ExtraTimeChargeItemData
}

type CalculateExpensePerWorkerResp struct {
	TotalExpense   float64
	BasicPriceName string
}

type CalculateStatutoryRateFromDepartmentPicResp struct {
	Rate              float64
	CustomerID        int64
	CustomerName      string
	DepartmentID      int64
	DepartmentName    string
	DepartmentPicName string
}

type CalculateOptionAmountResp struct {
	TotalAmount float64
	OptionData  map[int64]OptionData
}

type ExtraTimeChargeItemData struct {
	Price float64
}

type OptionData struct {
	Title  string
	Amount float64
}

type DailyReportAdditionData struct {
	Title           string
	AmountPerSite   float64
	AmountPerWorker float64
}

// SaveSiteReportReq represents the request structure for saving site report
type SaveSiteReportReq struct {
	ID                    int64                                  `json:"id"`
	WorkDate              string                                 `json:"work_date"`
	SiteName              string                                 `json:"site_name"`
	HasStatutory          bool                                   `json:"has_statutory"`
	ReportNumber          string                                 `json:"report_number"`
	DepartmentPicID       int64                                  `json:"department_pic_id"`
	BillDate              string                                 `json:"bill_date"`
	BasicPriceID          int64                                  `json:"basic_price_id"`
	DailyReportAdditions  []GetDetailListDailyReportAdditionResp `json:"daily_report_additions,omitempty"`
	TotalWorker           int64                                  `json:"total_worker"`
	DistrictBlockID       int64                                  `json:"district_block_id"`
	DistrictBlockUnit     int64                                  `json:"district_block_unit"`
	TransitPlaceBlockID   *int64                                 `json:"transit_place_block_id,omitempty"`
	TransitPlaceBlockUnit *int64                                 `json:"transit_place_block_unit,omitempty"`
	BStartTime            string                                 `json:"b_start_time"`
	BEndTime              string                                 `json:"b_end_time"`
	SStartTime            string                                 `json:"s_start_time"`
	SEndTime              string                                 `json:"s_end_time"`
	BreakTime             string                                 `json:"break_time"`
	LateEarlyWorker       int64                                  `json:"late_early_worker"`
	ExtraTimeCharge       []SaveSiteReportExtraTimeItem          `json:"extra_time_charge"`
	Note                  string                                 `json:"note"`
	NoteForInvoice        string                                 `json:"note_for_invoice"`
	Statutory             *SaveSiteReportStatutory               `json:"statutory,omitempty"`
	Option                []SaveSiteReportOption                 `json:"option"`
	Worker                []SaveSiteReportWorker                 `json:"worker"`

	// Parsed time fields (populated during validation)
	ParsedWorkDate   time.Time  `json:"-"`
	ParsedBillDate   time.Time  `json:"-"`
	ParsedBStartTime time.Time  `json:"-"`
	ParsedBEndTime   time.Time  `json:"-"`
	ParsedSStartTime time.Time  `json:"-"`
	ParsedSEndTime   time.Time  `json:"-"`
	ParsedBreakTime  *time.Time `json:"-"`
	UserID           int64      `json:"-"`
}

// SaveSiteReportExtraTimeItem represents extra time charge item
type SaveSiteReportExtraTimeItem struct {
	Time        string `json:"time"`
	TotalWorker int64  `json:"total_worker"`

	// Parsed time field
	ParsedTime time.Time `json:"-"`
}

// SaveSiteReportStatutory represents statutory data for site report
type SaveSiteReportStatutory struct {
	Adjustment float64 `json:"adjustment"`
	Note       string  `json:"note"`
}

// SaveSiteReportOption represents option data for site report
type SaveSiteReportOption struct {
	OptionID int64 `json:"option_id"`
}

// SaveSiteReportDailyReportAddition represents daily report addition data for site report
type SaveSiteReportDailyReportAddition struct {
	DailyReportAdditionID int64 `json:"daily_report_addition_id"`
}

// SaveSiteReportWorker represents worker data for site report
type SaveSiteReportWorker struct {
	ID               int64                               `json:"id"`
	WorkerID         int64                               `json:"worker_id"`
	StartTime        string                              `json:"start_time"`
	EndTime          string                              `json:"end_time"`
	BreakTime        string                              `json:"break_time"`
	TransportExpense float64                             `json:"transport_expense"`
	LeaderAllowance  float64                             `json:"leader_allowance"`
	DistantFeeID     *int64                              `json:"distant_fee_id,omitempty"`
	Qualifications   []SaveSiteReportWorkerQualification `json:"qualifications"`

	// Parsed time fields
	ParsedStartTime time.Time  `json:"-"`
	ParsedEndTime   time.Time  `json:"-"`
	ParsedBreakTime *time.Time `json:"-"`
}

// SaveSiteReportWorkerQualification represents worker qualification data
type SaveSiteReportWorkerQualification struct {
	QualificationID int64 `json:"qualification_id"`
}

// SaveSiteReportResp represents the response structure for saving site report
type SaveSiteReportResp struct {
	Message string `json:"message"`
}

// DoCreateSiteReportParam represents parameters for creating site report
type DoCreateSiteReportParam struct {
	Req      SaveSiteReportReq
	CalcResp DoSiteReportCalculationResp
	UserID   int64
}

// DoUpdateSiteReportParam represents parameters for updating site report
type DoUpdateSiteReportParam struct {
	Req      SaveSiteReportReq
	CalcResp DoSiteReportCalculationResp
	UserID   int64
}

// ManageSiteReportOptionsParam represents parameters for managing site report options
type ManageSiteReportOptionsParam struct {
	SiteReportID      int64
	RequestedOptions  []SaveSiteReportOption
	CalculatedOptions map[int64]OptionData
}

// ProcessOptionChangesParam represents parameters for processing option changes
type ProcessOptionChangesParam struct {
	SiteReportID       int64
	ExistingOptionMap  map[int64]bool
	RequestedOptionMap map[int64]bool
	OptionDataMap      map[int64]OptionData
}

// ManageSiteReportDailyReportAdditionsParam represents parameters for managing site report daily report additions
type ManageSiteReportDailyReportAdditionsParam struct {
	SiteReportID                   int64
	RequestedDailyReportAdditions  []SaveSiteReportDailyReportAddition
	CalculatedDailyReportAdditions map[int64]DailyReportAdditionData
}

// ProcessDailyReportAdditionChangesParam represents parameters for processing daily report addition changes
type ProcessDailyReportAdditionChangesParam struct {
	SiteReportID                    int64
	ExistingDailyReportAdditionMap  map[int64]bool
	RequestedDailyReportAdditionMap map[int64]bool
	DailyReportAdditionDataMap      map[int64]DailyReportAdditionData
}

// ExecuteBulkOptionOperationsParam represents parameters for executing bulk option operations
type ExecuteBulkOptionOperationsParam struct {
	SiteReportID int64
	ToUpdate     []sitereportoptionDmn.OptionUpdateData
	ToInsert     []sitereportoptionDmn.OptionItem
	ToDelete     []int64
}

// GetDeliverySlipReq represents the request parameters for getting delivery slip data
type GetDeliverySlipReq struct {
	SiteReportID int64 `query:"site_report_id"`
}

// GetDeliverySlipResp represents the response structure for delivery slip data
type GetDeliverySlipResp struct {
	IssueDate         string                     `json:"issue_date"`
	CustomerName      string                     `json:"customer_name"`
	DepartmentPicName string                     `json:"department_pic_name"`
	SiteName          string                     `json:"site_name"`
	WorkDate          string                     `json:"work_date"`
	Expiration        string                     `json:"expiration"`
	DefaultAddress    string                     `json:"default_address"`
	TotalAmount       float64                    `json:"total_amount"`
	MainTable         []GetDeliverySlipMainTable `json:"main_table"`
	NoteForInvoice    string                     `json:"note_for_invoice"`
	Statutory         GetDeliverySlipStatutory   `json:"statutory"`
}

type GetDeliverySlipMainTable struct {
	Item       string  `json:"item"`
	Quantity   int64   `json:"quantity"`
	Unit       string  `json:"unit"`
	Price      float64 `json:"price"`
	TotalPrice float64 `json:"total_price"`
}

// GetDeliverySlipStatutory represents the statutory data in delivery slip response
type GetDeliverySlipStatutory struct {
	Rate        float64 `json:"rate"`
	TotalAmount float64 `json:"total_amount"`
}

type BuildDeliverySlipParam struct {
	Report         sitereportDmn.SiteReport
	ReportSnapshot sitereportDmn.Snapshot
	CalcResp       DoSiteReportCalculationResp
	Resp           GetDeliverySlipResp
}
