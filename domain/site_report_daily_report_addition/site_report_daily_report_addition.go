package sitereportdailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	SiteReportDailyReportAdditionDomainItf interface {
		BulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error
		GetBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportDailyReportAddition, error)
		BulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error
		BulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx(ctx context.Context, tx *gorm.DB, siteReportID int64, dailyReportAdditionIDs []int64) error
	}

	SiteReportDailyReportAdditionResourceItf interface {
		bulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error
		getBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportDailyReportAddition, error)
		bulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error
		bulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx(ctx context.Context, tx *gorm.DB, siteReportID int64, dailyReportAdditionIDs []int64) error
	}
)

// BulkCreateWithTx creates multiple site report daily report addition records within a transaction
func (dmn SiteReportDailyReportAdditionDomain) BulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error {
	err := dmn.resource.bulkCreateWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// GetBySiteReportIDWithTx retrieves site report daily report additions by site report ID within a transaction
func (dmn SiteReportDailyReportAdditionDomain) GetBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportDailyReportAddition, error) {
	additions, err := dmn.resource.getBySiteReportIDWithTx(ctx, tx, param)
	if err != nil {
		return []SiteReportDailyReportAddition{}, log.LogError(err, nil)
	}
	return additions, nil
}

// BulkUpdateWithTx updates multiple site report daily report addition records within a transaction
func (dmn SiteReportDailyReportAdditionDomain) BulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error {
	err := dmn.resource.bulkUpdateWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// BulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx deletes site report daily report additions by site report ID and daily report addition IDs within a transaction
func (dmn SiteReportDailyReportAdditionDomain) BulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx(ctx context.Context, tx *gorm.DB, siteReportID int64, dailyReportAdditionIDs []int64) error {
	err := dmn.resource.bulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx(ctx, tx, siteReportID, dailyReportAdditionIDs)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}
